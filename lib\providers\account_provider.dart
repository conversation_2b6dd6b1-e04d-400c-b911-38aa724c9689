import 'package:flutter/foundation.dart';
import '../models/account.dart';
import '../services/database_helper.dart';

class AccountProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Account> _accounts = [];
  bool _isLoading = false;
  String _selectedCategory = 'عام';
  String? _selectedCurrency;

  List<Account> get accounts => _accounts;
  bool get isLoading => _isLoading;
  String get selectedCategory => _selectedCategory;
  String? get selectedCurrency => _selectedCurrency;

  List<Account> get filteredAccounts {
    if (_selectedCurrency == null) {
      return _accounts.where((account) => account.category == _selectedCategory).toList();
    }
    return _accounts
        .where((account) =>
            account.category == _selectedCategory &&
            account.currencyType == _selectedCurrency)
        .toList();
  }

  double get totalAmount {
    return filteredAccounts.fold(0.0, (sum, account) => sum + account.amount);
  }

  double get totalOwedToMe {
    return filteredAccounts
        .where((account) => account.debtType == 'له')
        .fold(0.0, (sum, account) => sum + account.amount);
  }

  double get totalIOwe {
    return filteredAccounts
        .where((account) => account.debtType == 'عليه')
        .fold(0.0, (sum, account) => sum + account.amount);
  }

  double get netAmount {
    return totalOwedToMe - totalIOwe;
  }

  Map<String, double> get amountsByDebtType {
    Map<String, double> amounts = {'له': 0.0, 'عليه': 0.0};
    for (var account in filteredAccounts) {
      amounts[account.debtType] = (amounts[account.debtType] ?? 0.0) + account.amount;
    }
    return amounts;
  }

  List<String> get usedCurrencies {
    return _accounts
        .where((account) => account.category == _selectedCategory)
        .map((account) => account.currencyType)
        .toSet()
        .toList();
  }

  void setSelectedCategory(String category) {
    _selectedCategory = category;
    _selectedCurrency = null;
    notifyListeners();
  }

  void setSelectedCurrency(String? currency) {
    _selectedCurrency = currency;
    notifyListeners();
  }

  Future<void> loadAccounts() async {
    _isLoading = true;
    notifyListeners();

    try {
      _accounts = await _databaseHelper.getAccounts();
    } catch (e) {
      debugPrint('Error loading accounts: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> loadAccountsByCategory(String category) async {
    _isLoading = true;
    notifyListeners();

    try {
      _accounts = await _databaseHelper.getAccountsByCategory(category);
    } catch (e) {
      debugPrint('Error loading accounts by category: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addAccount(Account account) async {
    try {
      final id = await _databaseHelper.insertAccount(account);
      final newAccount = account.copyWith(id: id);
      _accounts.add(newAccount);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding account: $e');
      rethrow;
    }
  }

  Future<void> updateAccount(Account account) async {
    try {
      await _databaseHelper.updateAccount(account);
      final index = _accounts.indexWhere((a) => a.id == account.id);
      if (index != -1) {
        _accounts[index] = account;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating account: $e');
      rethrow;
    }
  }

  Future<void> deleteAccount(int id) async {
    try {
      await _databaseHelper.deleteAccount(id);
      _accounts.removeWhere((account) => account.id == id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting account: $e');
      rethrow;
    }
  }

  Future<void> transferAccount(int accountId, String fromCategory, String toCategory) async {
    try {
      final accountIndex = _accounts.indexWhere((a) => a.id == accountId);
      if (accountIndex != -1) {
        final account = _accounts[accountIndex];
        final updatedAccount = account.copyWith(
          category: toCategory,
          updatedAt: DateTime.now(),
        );
        await updateAccount(updatedAccount);
      }
    } catch (e) {
      debugPrint('Error transferring account: $e');
      rethrow;
    }
  }

  List<Account> searchAccounts(String query) {
    if (query.isEmpty) return filteredAccounts;
    
    return filteredAccounts.where((account) {
      return account.name.toLowerCase().contains(query.toLowerCase()) ||
          account.phoneNumber.contains(query) ||
          account.details.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  List<Account> getAccountsSortedBy(String sortBy, {bool ascending = true}) {
    List<Account> sortedAccounts = List.from(filteredAccounts);
    
    switch (sortBy) {
      case 'name':
        sortedAccounts.sort((a, b) => ascending 
            ? a.name.compareTo(b.name) 
            : b.name.compareTo(a.name));
        break;
      case 'amount':
        sortedAccounts.sort((a, b) => ascending 
            ? a.amount.compareTo(b.amount) 
            : b.amount.compareTo(a.amount));
        break;
      case 'date':
        sortedAccounts.sort((a, b) => ascending 
            ? a.date.compareTo(b.date) 
            : b.date.compareTo(a.date));
        break;
      case 'createdAt':
        sortedAccounts.sort((a, b) => ascending 
            ? a.createdAt.compareTo(b.createdAt) 
            : b.createdAt.compareTo(a.createdAt));
        break;
      default:
        break;
    }
    
    return sortedAccounts;
  }

  void clearAccounts() {
    _accounts.clear();
    notifyListeners();
  }
}
