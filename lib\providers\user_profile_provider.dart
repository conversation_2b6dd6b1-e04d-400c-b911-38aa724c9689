import 'package:flutter/foundation.dart';
import '../models/user_profile.dart';
import '../services/database_helper.dart';

class UserProfileProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  UserProfile? _userProfile;
  bool _isLoading = false;

  UserProfile? get userProfile => _userProfile;
  bool get isLoading => _isLoading;
  bool get hasProfile => _userProfile != null;

  Future<void> loadUserProfile() async {
    _isLoading = true;
    notifyListeners();

    try {
      _userProfile = await _databaseHelper.getUserProfile();
    } catch (e) {
      debugPrint('Error loading user profile: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> createUserProfile(UserProfile profile) async {
    try {
      final id = await _databaseHelper.insertUserProfile(profile);
      _userProfile = profile.copyWith(id: id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error creating user profile: $e');
      rethrow;
    }
  }

  Future<void> updateUserProfile(UserProfile profile) async {
    try {
      await _databaseHelper.updateUserProfile(profile);
      _userProfile = profile;
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating user profile: $e');
      rethrow;
    }
  }

  Future<void> saveUserProfile({
    required String name,
    required String address,
    required String phoneNumber,
    required String email,
  }) async {
    final now = DateTime.now();
    
    if (_userProfile == null) {
      // Create new profile
      final profile = UserProfile(
        name: name,
        address: address,
        phoneNumber: phoneNumber,
        email: email,
        createdAt: now,
        updatedAt: now,
      );
      await createUserProfile(profile);
    } else {
      // Update existing profile
      final updatedProfile = _userProfile!.copyWith(
        name: name,
        address: address,
        phoneNumber: phoneNumber,
        email: email,
        updatedAt: now,
      );
      await updateUserProfile(updatedProfile);
    }
  }

  void clearUserProfile() {
    _userProfile = null;
    notifyListeners();
  }
}
