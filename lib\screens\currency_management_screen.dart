import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/currency.dart';
import '../providers/currency_provider.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../widgets/custom_app_bar.dart';

class CurrencyManagementScreen extends StatefulWidget {
  const CurrencyManagementScreen({super.key});

  @override
  State<CurrencyManagementScreen> createState() => _CurrencyManagementScreenState();
}

class _CurrencyManagementScreenState extends State<CurrencyManagementScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CurrencyProvider>().loadCurrencies();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'إدارة العملات',
        automaticallyImplyLeading: true,
      ),
      body: Consumer<CurrencyProvider>(
        builder: (context, currencyProvider, child) {
          if (currencyProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (currencyProvider.currencies.isEmpty) {
            return _buildEmptyState();
          }

          return ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: currencyProvider.currencies.length,
            itemBuilder: (context, index) {
              final currency = currencyProvider.currencies[index];
              return _buildCurrencyCard(currency, currencyProvider);
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCurrencyDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.monetization_on_outlined,
            size: 80,
            color: AppTheme.mediumGray,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد عملات',
            style: AppTheme.headingMedium.copyWith(color: AppTheme.mediumGray),
          ),
          const SizedBox(height: 8),
          Text(
            'قم بإضافة عملة جديدة',
            style: AppTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => _showCurrencyDialog(),
            child: const Text('إضافة عملة'),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencyCard(Currency currency, CurrencyProvider currencyProvider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: currency.isDefault 
              ? AppTheme.primaryPurple 
              : AppTheme.primaryPurple.withOpacity(0.1),
          child: Text(
            currency.symbol,
            style: TextStyle(
              color: currency.isDefault ? AppTheme.white : AppTheme.primaryPurple,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Row(
          children: [
            Text(
              currency.name,
              style: AppTheme.headingSmall,
            ),
            if (currency.isDefault) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: AppTheme.primaryPurple,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'افتراضي',
                  style: TextStyle(
                    color: AppTheme.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        subtitle: Text(
          '${currency.code} - ${currency.symbol}',
          style: AppTheme.bodyMedium.copyWith(
            color: AppTheme.mediumGray,
          ),
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showCurrencyDialog(currency: currency);
                break;
              case 'delete':
                _deleteCurrency(currency);
                break;
              case 'setDefault':
                _setDefaultCurrency(currency, currencyProvider);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: AppTheme.primaryPurple),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            if (!currency.isDefault)
              const PopupMenuItem(
                value: 'setDefault',
                child: Row(
                  children: [
                    Icon(Icons.star, color: AppTheme.warning),
                    SizedBox(width: 8),
                    Text('جعل افتراضي'),
                  ],
                ),
              ),
            if (currency.code != AppConstants.defaultCurrency)
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: AppTheme.error),
                    SizedBox(width: 8),
                    Text('حذف'),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showCurrencyDialog({Currency? currency}) {
    final nameController = TextEditingController(text: currency?.name ?? '');
    final symbolController = TextEditingController(text: currency?.symbol ?? '');
    final codeController = TextEditingController(text: currency?.code ?? '');
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(currency == null ? 'إضافة عملة جديدة' : 'تعديل العملة'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم العملة',
                  prefixIcon: Icon(Icons.monetization_on),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return AppConstants.validationRequired;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: symbolController,
                decoration: const InputDecoration(
                  labelText: 'رمز العملة',
                  prefixIcon: Icon(Icons.currency_exchange),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return AppConstants.validationRequired;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: codeController,
                decoration: const InputDecoration(
                  labelText: 'كود العملة',
                  prefixIcon: Icon(Icons.code),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return AppConstants.validationRequired;
                  }
                  if (value.trim() != currency?.code &&
                      context.read<CurrencyProvider>().currencyExists(value.trim())) {
                    return 'كود العملة موجود بالفعل';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _saveCurrency(
              formKey,
              nameController.text,
              symbolController.text,
              codeController.text,
              currency,
            ),
            child: Text(currency == null ? 'إضافة' : 'حفظ'),
          ),
        ],
      ),
    );
  }

  void _saveCurrency(
    GlobalKey<FormState> formKey,
    String name,
    String symbol,
    String code,
    Currency? existingCurrency,
  ) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      final now = DateTime.now();
      final currencyProvider = context.read<CurrencyProvider>();

      if (existingCurrency == null) {
        // Add new currency
        final currency = Currency(
          name: name.trim(),
          symbol: symbol.trim(),
          code: code.trim().toUpperCase(),
          isDefault: false,
          createdAt: now,
          updatedAt: now,
        );
        await currencyProvider.addCurrency(currency);
        AppHelpers.showSuccessSnackbar(context, 'تم إضافة العملة بنجاح');
      } else {
        // Update existing currency
        final updatedCurrency = existingCurrency.copyWith(
          name: name.trim(),
          symbol: symbol.trim(),
          code: code.trim().toUpperCase(),
          updatedAt: now,
        );
        await currencyProvider.updateCurrency(updatedCurrency);
        AppHelpers.showSuccessSnackbar(context, 'تم تحديث العملة بنجاح');
      }

      Navigator.of(context).pop();
    } catch (e) {
      AppHelpers.showErrorSnackbar(context, 'حدث خطأ أثناء حفظ العملة');
    }
  }

  void _setDefaultCurrency(Currency currency, CurrencyProvider currencyProvider) async {
    try {
      await currencyProvider.setDefaultCurrency(currency.id!);
      AppHelpers.showSuccessSnackbar(context, 'تم تعيين العملة الافتراضية');
    } catch (e) {
      AppHelpers.showErrorSnackbar(context, 'حدث خطأ أثناء تعيين العملة الافتراضية');
    }
  }

  void _deleteCurrency(Currency currency) async {
    if (currency.code == AppConstants.defaultCurrency) {
      AppHelpers.showErrorSnackbar(context, 'لا يمكن حذف العملة الافتراضية');
      return;
    }

    final confirmed = await AppHelpers.showConfirmDialog(
      context,
      title: 'حذف العملة',
      content: 'هل أنت متأكد من حذف هذه العملة؟',
    );

    if (confirmed) {
      try {
        await context.read<CurrencyProvider>().deleteCurrency(currency.id!);
        AppHelpers.showSuccessSnackbar(context, 'تم حذف العملة بنجاح');
      } catch (e) {
        AppHelpers.showErrorSnackbar(context, 'حدث خطأ أثناء حذف العملة');
      }
    }
  }
}
