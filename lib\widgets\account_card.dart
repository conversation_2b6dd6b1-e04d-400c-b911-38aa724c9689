import 'package:flutter/material.dart';
import '../models/account.dart';
import '../utils/app_theme.dart';
import '../utils/helpers.dart';

class AccountCard extends StatelessWidget {
  final Account account;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;
  final VoidCallback? onEdit;
  final VoidCallback? onCall;
  final VoidCallback? onWhatsApp;

  const AccountCard({
    super.key,
    required this.account,
    this.onTap,
    this.onDelete,
    this.onEdit,
    this.onCall,
    this.onWhatsApp,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppTheme.cardBorderRadius),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 12),
              _buildAccountInfo(),
              if (account.details.isNotEmpty) ...[
                const SizedBox(height: 8),
                _buildDetails(),
              ],
              const SizedBox(height: 12),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        CircleAvatar(
          backgroundColor: AppHelpers.getDebtTypeColor(
            account.debtType,
          ).withOpacity(0.1),
          child: Icon(
            AppHelpers.getDebtTypeIcon(account.debtType),
            color: AppHelpers.getDebtTypeColor(account.debtType),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                account.name,
                style: AppTheme.headingSmall,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                AppHelpers.formatPhoneNumber(account.phoneNumber),
                style: AppTheme.bodyMedium.copyWith(color: AppTheme.mediumGray),
              ),
            ],
          ),
        ),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildActionButtons() {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert, color: AppTheme.mediumGray),
      onSelected: (value) {
        switch (value) {
          case 'edit':
            onEdit?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
          case 'call':
            onCall?.call();
            break;
          case 'whatsapp':
            onWhatsApp?.call();
            break;
        }
      },
      itemBuilder:
          (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: AppTheme.primaryPurple),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'call',
              child: Row(
                children: [
                  Icon(Icons.phone, color: AppTheme.info),
                  SizedBox(width: 8),
                  Text('اتصال'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'whatsapp',
              child: Row(
                children: [
                  Icon(Icons.message, color: AppTheme.success),
                  SizedBox(width: 8),
                  Text('واتساب'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: AppTheme.error),
                  SizedBox(width: 8),
                  Text('حذف'),
                ],
              ),
            ),
          ],
    );
  }

  Widget _buildAccountInfo() {
    return Row(
      children: [
        Expanded(
          child: _buildInfoItem(
            icon: Icons.account_balance_wallet,
            label: 'المبلغ',
            value: AppHelpers.formatAmount(account.amount),
            color: AppHelpers.getDebtTypeColor(account.debtType),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildInfoItem(
            icon: Icons.monetization_on,
            label: 'العملة',
            value: account.currencyType,
            color: AppTheme.primaryPurple,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Text(label, style: AppTheme.bodySmall.copyWith(color: color)),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTheme.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetails() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.lightGray,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التفاصيل',
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.mediumGray,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(account.details, style: AppTheme.bodyMedium),
        ],
      ),
    );
  }

  Widget _buildFooter() {
    return Row(
      children: [
        Icon(Icons.calendar_today, size: 14, color: AppTheme.mediumGray),
        const SizedBox(width: 4),
        Text(
          AppHelpers.formatDate(account.date),
          style: AppTheme.bodySmall.copyWith(color: AppTheme.mediumGray),
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: AppHelpers.getDebtTypeColor(
              account.debtType,
            ).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            account.debtType,
            style: AppTheme.bodySmall.copyWith(
              color: AppHelpers.getDebtTypeColor(account.debtType),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
