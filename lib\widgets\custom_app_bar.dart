import 'package:flutter/material.dart';
import '../utils/app_theme.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool isSearching;
  final TextEditingController? searchController;
  final ValueChanged<String>? onSearchChanged;
  final VoidCallback? onSearchToggle;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;

  const CustomAppBar({
    super.key,
    required this.title,
    this.isSearching = false,
    this.searchController,
    this.onSearchChanged,
    this.onSearchToggle,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: isSearching ? _buildSearchField() : Text(title),
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      actions: _buildActions(),
      backgroundColor: AppTheme.primaryPurple,
      foregroundColor: AppTheme.white,
      elevation: 0,
    );
  }

  Widget _buildSearchField() {
    return TextField(
      controller: searchController,
      onChanged: onSearchChanged,
      autofocus: true,
      style: const TextStyle(color: AppTheme.white),
      decoration: const InputDecoration(
        hintText: 'البحث...',
        hintStyle: TextStyle(color: AppTheme.accentPurple),
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(horizontal: 16),
      ),
    );
  }

  List<Widget> _buildActions() {
    List<Widget> actionWidgets = [];

    if (onSearchToggle != null) {
      actionWidgets.add(
        IconButton(
          icon: Icon(isSearching ? Icons.close : Icons.search),
          onPressed: onSearchToggle,
        ),
      );
    }

    if (actions != null) {
      actionWidgets.addAll(actions!);
    }

    return actionWidgets;
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
