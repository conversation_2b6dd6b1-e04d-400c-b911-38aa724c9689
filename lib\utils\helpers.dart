import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'constants.dart';

class AppHelpers {
  // Date Formatting
  static String formatDate(DateTime date) {
    return DateFormat(AppConstants.displayDateFormat).format(date);
  }

  static String formatDateTime(DateTime dateTime) {
    return DateFormat(AppConstants.displayDateTimeFormat).format(dateTime);
  }

  static String formatDateForDatabase(DateTime date) {
    return DateFormat(AppConstants.dateFormat).format(date);
  }

  static DateTime? parseDate(String dateString) {
    try {
      return DateFormat(AppConstants.dateFormat).parse(dateString);
    } catch (e) {
      return null;
    }
  }

  // Amount Formatting
  static String formatAmount(double amount, {String? currencySymbol}) {
    final formatter = NumberFormat('#,##0.00');
    final formattedAmount = formatter.format(amount);
    if (currencySymbol != null) {
      return '$currencySymbol $formattedAmount';
    }
    return formattedAmount;
  }

  static String formatAmountWithCurrency(double amount, String currencySymbol) {
    return '${formatAmount(amount)} $currencySymbol';
  }

  static double? parseAmount(String amountString) {
    try {
      // Remove currency symbols and spaces
      String cleanAmount = amountString.replaceAll(RegExp(r'[^\d.-]'), '');
      return double.parse(cleanAmount);
    } catch (e) {
      return null;
    }
  }

  // Phone Number Formatting
  static String formatPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    String digits = phoneNumber.replaceAll(RegExp(r'\D'), '');

    // Format based on length
    if (digits.length == 10) {
      return '${digits.substring(0, 3)} ${digits.substring(3, 6)} ${digits.substring(6)}';
    } else if (digits.length == 13 && digits.startsWith('966')) {
      return '+966 ${digits.substring(3, 5)} ${digits.substring(5, 8)} ${digits.substring(8)}';
    }

    return phoneNumber; // Return original if can't format
  }

  static bool isValidPhoneNumber(String phoneNumber) {
    String digits = phoneNumber.replaceAll(RegExp(r'\D'), '');
    return digits.length >= 10 && digits.length <= 15;
  }

  // Email Validation
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // Text Validation
  static bool isValidName(String name) {
    return name.trim().isNotEmpty && name.length <= AppConstants.maxNameLength;
  }

  static bool isValidDetails(String details) {
    return details.length <= AppConstants.maxDetailsLength;
  }

  static bool isValidAmount(double amount) {
    return amount >= AppConstants.minAmount && amount <= AppConstants.maxAmount;
  }

  // Color Helpers
  static Color getDebtTypeColor(String debtType) {
    switch (debtType) {
      case AppConstants.owedToMe:
        return Colors.green;
      case AppConstants.iOwe:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  static IconData getDebtTypeIcon(String debtType) {
    switch (debtType) {
      case AppConstants.owedToMe:
        return Icons.arrow_upward;
      case AppConstants.iOwe:
        return Icons.arrow_downward;
      default:
        return Icons.remove;
    }
  }

  // Snackbar Helpers
  static void showSuccessSnackbar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  static void showErrorSnackbar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  static void showInfoSnackbar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Dialog Helpers
  static Future<bool> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    String confirmText = 'نعم',
    String cancelText = 'لا',
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(cancelText),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(confirmText),
              ),
            ],
          ),
    );
    return result ?? false;
  }

  static void showInfoDialog(
    BuildContext context, {
    required String title,
    required String content,
    String buttonText = 'موافق',
  }) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(buttonText),
              ),
            ],
          ),
    );
  }

  // Loading Dialog
  static void showLoadingDialog(BuildContext context, {String? message}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                if (message != null) ...[
                  const SizedBox(height: 16),
                  Text(message),
                ],
              ],
            ),
          ),
    );
  }

  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context).pop();
  }

  // Text Direction Helper
  static TextDirection getTextDirection(String text) {
    // Check if text contains Arabic characters
    if (RegExp(r'[\u0600-\u06FF]').hasMatch(text)) {
      return TextDirection.rtl;
    }
    return TextDirection.ltr;
  }

  // File Name Helpers
  static String generateFileName(String prefix, String extension) {
    final now = DateTime.now();
    final timestamp = DateFormat('yyyyMMdd_HHmmss').format(now);
    return '${prefix}_$timestamp.$extension';
  }

  // Search Helpers
  static bool matchesSearch(String text, String query) {
    if (query.isEmpty) return true;
    return text.toLowerCase().contains(query.toLowerCase());
  }

  // Sorting Helpers
  static int compareStrings(String a, String b, {bool ascending = true}) {
    return ascending ? a.compareTo(b) : b.compareTo(a);
  }

  static int compareNumbers(num a, num b, {bool ascending = true}) {
    return ascending ? a.compareTo(b) : b.compareTo(a);
  }

  static int compareDates(DateTime a, DateTime b, {bool ascending = true}) {
    return ascending ? a.compareTo(b) : b.compareTo(a);
  }

  // Network Helpers
  static bool isValidUrl(String url) {
    try {
      Uri.parse(url);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Platform Helpers
  static bool get isArabic => true; // For this app, we're using Arabic

  // Debounce Helper
  static Timer? _debounceTimer;

  static void debounce(Duration duration, VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(duration, callback);
  }
}
