import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';
import '../models/account.dart';
import '../providers/account_provider.dart';
import '../providers/category_provider.dart';
import '../providers/currency_provider.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../widgets/custom_app_bar.dart';

class AccountFormScreen extends StatefulWidget {
  final Account? account;

  const AccountFormScreen({super.key, this.account});

  @override
  State<AccountFormScreen> createState() => _AccountFormScreenState();
}

class _AccountFormScreenState extends State<AccountFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();
  final _detailsController = TextEditingController();

  String _selectedCategory = AppConstants.defaultCategory;
  String _selectedCurrency = AppConstants.defaultCurrency;
  String _selectedDebtType = AppConstants.owedToMe;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.account != null) {
      final account = widget.account!;
      _nameController.text = account.name;
      _phoneController.text = account.phoneNumber;
      _amountController.text = account.amount.toString();
      _detailsController.text = account.details;
      _selectedCategory = account.category;
      _selectedCurrency = account.currencyType;
      _selectedDebtType = account.debtType;
      _selectedDate = account.date;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _amountController.dispose();
    _detailsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: widget.account == null ? 'إضافة حساب جديد' : 'تعديل الحساب',
        actions: [
          if (widget.account != null)
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _deleteAccount,
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildNameField(),
              const SizedBox(height: 16),
              _buildPhoneField(),
              const SizedBox(height: 16),
              _buildAmountField(),
              const SizedBox(height: 16),
              _buildCurrencyField(),
              const SizedBox(height: 16),
              _buildCategoryField(),
              const SizedBox(height: 16),
              _buildDebtTypeField(),
              const SizedBox(height: 16),
              _buildDateField(),
              const SizedBox(height: 16),
              _buildDetailsField(),
              const SizedBox(height: 32),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: const InputDecoration(
        labelText: AppConstants.labelName,
        prefixIcon: Icon(Icons.person),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppConstants.validationRequired;
        }
        if (!AppHelpers.isValidName(value)) {
          return AppConstants.validationMaxLength;
        }
        return null;
      },
    );
  }

  Widget _buildPhoneField() {
    return IntlPhoneField(
      controller: _phoneController,
      decoration: const InputDecoration(
        labelText: AppConstants.labelPhone,
        border: OutlineInputBorder(),
      ),
      initialCountryCode: 'SA',
      validator: (phone) {
        if (phone == null || phone.number.isEmpty) {
          return AppConstants.validationRequired;
        }
        if (!AppHelpers.isValidPhoneNumber(phone.completeNumber)) {
          return AppConstants.validationInvalidPhone;
        }
        return null;
      },
      onChanged: (phone) {
        _phoneController.text = phone.completeNumber;
      },
    );
  }

  Widget _buildAmountField() {
    return TextFormField(
      controller: _amountController,
      decoration: const InputDecoration(
        labelText: AppConstants.labelAmount,
        prefixIcon: Icon(Icons.monetization_on),
      ),
      keyboardType: TextInputType.number,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppConstants.validationRequired;
        }
        final amount = AppHelpers.parseAmount(value);
        if (amount == null) {
          return AppConstants.validationInvalidAmount;
        }
        if (!AppHelpers.isValidAmount(amount)) {
          return AppConstants.validationInvalidAmount;
        }
        return null;
      },
    );
  }

  Widget _buildCurrencyField() {
    return Consumer<CurrencyProvider>(
      builder: (context, currencyProvider, child) {
        return DropdownButtonFormField<String>(
          value: _selectedCurrency,
          decoration: const InputDecoration(
            labelText: AppConstants.labelCurrency,
            prefixIcon: Icon(Icons.currency_exchange),
          ),
          items:
              currencyProvider.currencies.map((currency) {
                return DropdownMenuItem(
                  value: currency.name,
                  child: Text('${currency.name} (${currency.symbol})'),
                );
              }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCurrency = value!;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return AppConstants.validationRequired;
            }
            return null;
          },
        );
      },
    );
  }

  Widget _buildCategoryField() {
    return Consumer<CategoryProvider>(
      builder: (context, categoryProvider, child) {
        return DropdownButtonFormField<String>(
          value: _selectedCategory,
          decoration: const InputDecoration(
            labelText: AppConstants.labelCategory,
            prefixIcon: Icon(Icons.category),
          ),
          items:
              categoryProvider.categories.map<DropdownMenuItem<String>>((
                category,
              ) {
                return DropdownMenuItem<String>(
                  value: category.name,
                  child: Text(category.name),
                );
              }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedCategory = value!;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return AppConstants.validationRequired;
            }
            return null;
          },
        );
      },
    );
  }

  Widget _buildDebtTypeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppConstants.labelDebtType,
          style: AppTheme.bodyMedium.copyWith(color: AppTheme.mediumGray),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: RadioListTile<String>(
                title: const Text(AppConstants.owedToMe),
                value: AppConstants.owedToMe,
                groupValue: _selectedDebtType,
                onChanged: (value) {
                  setState(() {
                    _selectedDebtType = value!;
                  });
                },
                activeColor: AppTheme.owedToMe,
              ),
            ),
            Expanded(
              child: RadioListTile<String>(
                title: const Text(AppConstants.iOwe),
                value: AppConstants.iOwe,
                groupValue: _selectedDebtType,
                onChanged: (value) {
                  setState(() {
                    _selectedDebtType = value!;
                  });
                },
                activeColor: AppTheme.iOwe,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField() {
    return InkWell(
      onTap: _selectDate,
      child: InputDecorator(
        decoration: const InputDecoration(
          labelText: AppConstants.labelDate,
          prefixIcon: Icon(Icons.calendar_today),
        ),
        child: Text(
          AppHelpers.formatDate(_selectedDate),
          style: AppTheme.bodyMedium,
        ),
      ),
    );
  }

  Widget _buildDetailsField() {
    return TextFormField(
      controller: _detailsController,
      decoration: const InputDecoration(
        labelText: AppConstants.labelDetails,
        prefixIcon: Icon(Icons.notes),
        alignLabelWithHint: true,
      ),
      maxLines: 3,
      validator: (value) {
        if (value != null && !AppHelpers.isValidDetails(value)) {
          return AppConstants.validationMaxLength;
        }
        return null;
      },
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text(AppConstants.buttonCancel),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveAccount,
            child:
                _isLoading
                    ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                    : Text(
                      widget.account == null
                          ? AppConstants.buttonAdd
                          : AppConstants.buttonSave,
                    ),
          ),
        ),
      ],
    );
  }

  void _selectDate() async {
    DatePicker.showDatePicker(
      context,
      showTitleActions: true,
      minTime: DateTime(2000),
      maxTime: DateTime.now().add(const Duration(days: 365)),
      currentTime: _selectedDate,
      locale: LocaleType.ar,
      onConfirm: (date) {
        setState(() {
          _selectedDate = date;
        });
      },
    );
  }

  void _saveAccount() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final now = DateTime.now();
      final amount = AppHelpers.parseAmount(_amountController.text)!;

      final account = Account(
        id: widget.account?.id,
        name: _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        amount: amount,
        details: _detailsController.text.trim(),
        currencyType: _selectedCurrency,
        date: _selectedDate,
        debtType: _selectedDebtType,
        category: _selectedCategory,
        createdAt: widget.account?.createdAt ?? now,
        updatedAt: now,
      );

      final accountProvider = context.read<AccountProvider>();

      if (widget.account == null) {
        await accountProvider.addAccount(account);
        AppHelpers.showSuccessSnackbar(context, 'تم إضافة الحساب بنجاح');
      } else {
        await accountProvider.updateAccount(account);
        AppHelpers.showSuccessSnackbar(context, 'تم تحديث الحساب بنجاح');
      }

      Navigator.of(context).pop();
    } catch (e) {
      AppHelpers.showErrorSnackbar(context, 'حدث خطأ أثناء حفظ الحساب');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _deleteAccount() async {
    if (widget.account == null) return;

    final confirmed = await AppHelpers.showConfirmDialog(
      context,
      title: 'حذف الحساب',
      content: 'هل أنت متأكد من حذف هذا الحساب؟',
    );

    if (confirmed) {
      try {
        await context.read<AccountProvider>().deleteAccount(
          widget.account!.id!,
        );
        AppHelpers.showSuccessSnackbar(context, 'تم حذف الحساب بنجاح');
        Navigator.of(context).pop();
      } catch (e) {
        AppHelpers.showErrorSnackbar(context, 'حدث خطأ أثناء حذف الحساب');
      }
    }
  }
}
