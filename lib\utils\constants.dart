class AppConstants {
  // App Information
  static const String appName = 'دليل الحسابات';
  static const String appVersion = '1.0';
  
  // Default Values
  static const String defaultCategory = 'عام';
  static const String defaultCurrency = 'SAR';
  
  // Debt Types
  static const String owedToMe = 'له';
  static const String iOwe = 'عليه';
  
  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayDateTimeFormat = 'dd/MM/yyyy HH:mm';
  
  // Validation
  static const int maxNameLength = 50;
  static const int maxDetailsLength = 200;
  static const int maxPhoneLength = 15;
  static const int maxEmailLength = 100;
  static const int maxAddressLength = 200;
  static const double maxAmount = 999999999.99;
  static const double minAmount = 0.01;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 8.0;
  static const double cardBorderRadius = 12.0;
  static const double buttonHeight = 48.0;
  static const double inputHeight = 56.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Database
  static const String databaseName = 'accounts.db';
  static const int databaseVersion = 1;
  
  // Table Names
  static const String accountsTable = 'accounts';
  static const String categoriesTable = 'categories';
  static const String currenciesTable = 'currencies';
  static const String userProfileTable = 'user_profile';
  
  // Shared Preferences Keys
  static const String keyFirstLaunch = 'first_launch';
  static const String keySelectedCategory = 'selected_category';
  static const String keySelectedCurrency = 'selected_currency';
  static const String keyThemeMode = 'theme_mode';
  static const String keyLanguage = 'language';
  static const String keyBiometricEnabled = 'biometric_enabled';
  
  // Export Types
  static const String exportTypePdf = 'pdf';
  static const String exportTypeExcel = 'excel';
  static const String exportTypeCsv = 'csv';
  
  // File Names
  static const String pdfFileName = 'accounts_report';
  static const String excelFileName = 'accounts_data';
  static const String csvFileName = 'accounts_export';
  
  // Error Messages
  static const String errorGeneral = 'حدث خطأ غير متوقع';
  static const String errorNetwork = 'خطأ في الاتصال';
  static const String errorDatabase = 'خطأ في قاعدة البيانات';
  static const String errorValidation = 'خطأ في التحقق من البيانات';
  static const String errorPermission = 'ليس لديك صلاحية للوصول';
  
  // Success Messages
  static const String successSave = 'تم الحفظ بنجاح';
  static const String successUpdate = 'تم التحديث بنجاح';
  static const String successDelete = 'تم الحذف بنجاح';
  static const String successExport = 'تم التصدير بنجاح';
  
  // Confirmation Messages
  static const String confirmDelete = 'هل أنت متأكد من الحذف؟';
  static const String confirmUpdate = 'هل تريد حفظ التغييرات؟';
  static const String confirmExit = 'هل تريد الخروج؟';
  
  // Validation Messages
  static const String validationRequired = 'هذا الحقل مطلوب';
  static const String validationInvalidEmail = 'البريد الإلكتروني غير صحيح';
  static const String validationInvalidPhone = 'رقم الهاتف غير صحيح';
  static const String validationInvalidAmount = 'المبلغ غير صحيح';
  static const String validationMaxLength = 'تجاوز الحد الأقصى للأحرف';
  static const String validationMinAmount = 'المبلغ أقل من الحد الأدنى';
  static const String validationMaxAmount = 'المبلغ أكبر من الحد الأقصى';
  
  // Arabic Labels
  static const String labelName = 'الاسم';
  static const String labelPhone = 'رقم الهاتف';
  static const String labelAmount = 'المبلغ';
  static const String labelDetails = 'التفاصيل';
  static const String labelCurrency = 'نوع العملة';
  static const String labelDate = 'التاريخ';
  static const String labelCategory = 'الفئة';
  static const String labelDebtType = 'نوع الدين';
  static const String labelAddress = 'العنوان';
  static const String labelEmail = 'البريد الإلكتروني';
  
  // Button Labels
  static const String buttonSave = 'حفظ';
  static const String buttonCancel = 'إلغاء';
  static const String buttonDelete = 'حذف';
  static const String buttonEdit = 'تعديل';
  static const String buttonAdd = 'إضافة';
  static const String buttonExport = 'تصدير';
  static const String buttonShare = 'مشاركة';
  static const String buttonSearch = 'بحث';
  static const String buttonFilter = 'تصفية';
  static const String buttonSort = 'ترتيب';
  static const String buttonSettings = 'الإعدادات';
  
  // Menu Items
  static const String menuAccounts = 'الحسابات';
  static const String menuCategories = 'الفئات';
  static const String menuCurrencies = 'العملات';
  static const String menuProfile = 'البيانات الشخصية';
  static const String menuSettings = 'الإعدادات';
  static const String menuAbout = 'حول التطبيق';
  
  // Status Labels
  static const String statusActive = 'نشط';
  static const String statusInactive = 'غير نشط';
  static const String statusPending = 'في الانتظار';
  static const String statusCompleted = 'مكتمل';
  
  // Sort Options
  static const String sortByName = 'الاسم';
  static const String sortByAmount = 'المبلغ';
  static const String sortByDate = 'التاريخ';
  static const String sortByCreated = 'تاريخ الإنشاء';
  
  // Filter Options
  static const String filterAll = 'الكل';
  static const String filterOwedToMe = 'له';
  static const String filterIOwe = 'عليه';
  
  // Default Currencies
  static const List<Map<String, String>> defaultCurrencies = [
    {'name': 'ريال سعودي', 'symbol': 'ر.س', 'code': 'SAR'},
    {'name': 'دولار أمريكي', 'symbol': '\$', 'code': 'USD'},
    {'name': 'يورو', 'symbol': '€', 'code': 'EUR'},
    {'name': 'جنيه إسترليني', 'symbol': '£', 'code': 'GBP'},
    {'name': 'درهم إماراتي', 'symbol': 'د.إ', 'code': 'AED'},
    {'name': 'دينار كويتي', 'symbol': 'د.ك', 'code': 'KWD'},
    {'name': 'ريال قطري', 'symbol': 'ر.ق', 'code': 'QAR'},
    {'name': 'دينار بحريني', 'symbol': 'د.ب', 'code': 'BHD'},
  ];
}
