import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/category.dart';
import '../providers/category_provider.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../widgets/custom_app_bar.dart';

class CategoryManagementScreen extends StatefulWidget {
  const CategoryManagementScreen({super.key});

  @override
  State<CategoryManagementScreen> createState() => _CategoryManagementScreenState();
}

class _CategoryManagementScreenState extends State<CategoryManagementScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CategoryProvider>().loadCategories();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'إدارة الفئات',
        automaticallyImplyLeading: true,
      ),
      body: Consumer<CategoryProvider>(
        builder: (context, categoryProvider, child) {
          if (categoryProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (categoryProvider.categories.isEmpty) {
            return _buildEmptyState();
          }

          return ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: categoryProvider.categories.length,
            itemBuilder: (context, index) {
              final category = categoryProvider.categories[index];
              return _buildCategoryCard(category);
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCategoryDialog(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.category_outlined,
            size: 80,
            color: AppTheme.mediumGray,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد فئات',
            style: AppTheme.headingMedium.copyWith(color: AppTheme.mediumGray),
          ),
          const SizedBox(height: 8),
          Text(
            'قم بإضافة فئة جديدة',
            style: AppTheme.bodyMedium,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => _showCategoryDialog(),
            child: const Text('إضافة فئة'),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(Category category) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppTheme.primaryPurple.withOpacity(0.1),
          child: const Icon(
            Icons.category,
            color: AppTheme.primaryPurple,
          ),
        ),
        title: Text(
          category.name,
          style: AppTheme.headingSmall,
        ),
        subtitle: category.description.isNotEmpty
            ? Text(
                category.description,
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.mediumGray,
                ),
              )
            : null,
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showCategoryDialog(category: category);
                break;
              case 'delete':
                _deleteCategory(category);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: AppTheme.primaryPurple),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            if (category.name != AppConstants.defaultCategory)
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: AppTheme.error),
                    SizedBox(width: 8),
                    Text('حذف'),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showCategoryDialog({Category? category}) {
    final nameController = TextEditingController(text: category?.name ?? '');
    final descriptionController = TextEditingController(text: category?.description ?? '');
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(category == null ? 'إضافة فئة جديدة' : 'تعديل الفئة'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الفئة',
                  prefixIcon: Icon(Icons.category),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return AppConstants.validationRequired;
                  }
                  if (value.trim() != category?.name &&
                      context.read<CategoryProvider>().categoryExists(value.trim())) {
                    return 'اسم الفئة موجود بالفعل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف (اختياري)',
                  prefixIcon: Icon(Icons.description),
                ),
                maxLines: 2,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _saveCategory(
              formKey,
              nameController.text,
              descriptionController.text,
              category,
            ),
            child: Text(category == null ? 'إضافة' : 'حفظ'),
          ),
        ],
      ),
    );
  }

  void _saveCategory(
    GlobalKey<FormState> formKey,
    String name,
    String description,
    Category? existingCategory,
  ) async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      final now = DateTime.now();
      final categoryProvider = context.read<CategoryProvider>();

      if (existingCategory == null) {
        // Add new category
        final category = Category(
          name: name.trim(),
          description: description.trim(),
          createdAt: now,
          updatedAt: now,
        );
        await categoryProvider.addCategory(category);
        AppHelpers.showSuccessSnackbar(context, 'تم إضافة الفئة بنجاح');
      } else {
        // Update existing category
        final updatedCategory = existingCategory.copyWith(
          name: name.trim(),
          description: description.trim(),
          updatedAt: now,
        );
        await categoryProvider.updateCategory(updatedCategory);
        AppHelpers.showSuccessSnackbar(context, 'تم تحديث الفئة بنجاح');
      }

      Navigator.of(context).pop();
    } catch (e) {
      AppHelpers.showErrorSnackbar(context, 'حدث خطأ أثناء حفظ الفئة');
    }
  }

  void _deleteCategory(Category category) async {
    if (category.name == AppConstants.defaultCategory) {
      AppHelpers.showErrorSnackbar(context, 'لا يمكن حذف الفئة الافتراضية');
      return;
    }

    final confirmed = await AppHelpers.showConfirmDialog(
      context,
      title: 'حذف الفئة',
      content: 'هل أنت متأكد من حذف هذه الفئة؟\nسيتم نقل جميع الحسابات إلى الفئة العامة.',
    );

    if (confirmed) {
      try {
        await context.read<CategoryProvider>().deleteCategory(category.id!);
        AppHelpers.showSuccessSnackbar(context, 'تم حذف الفئة بنجاح');
      } catch (e) {
        AppHelpers.showErrorSnackbar(context, 'حدث خطأ أثناء حذف الفئة');
      }
    }
  }
}
