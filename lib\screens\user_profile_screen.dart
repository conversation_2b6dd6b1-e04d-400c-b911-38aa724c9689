import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import '../providers/user_profile_provider.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../widgets/custom_app_bar.dart';

class UserProfileScreen extends StatefulWidget {
  const UserProfileScreen({super.key});

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserProfile();
  }

  void _loadUserProfile() async {
    final userProfileProvider = context.read<UserProfileProvider>();
    await userProfileProvider.loadUserProfile();
    
    if (userProfileProvider.hasProfile) {
      final profile = userProfileProvider.userProfile!;
      _nameController.text = profile.name;
      _addressController.text = profile.address;
      _phoneController.text = profile.phoneNumber;
      _emailController.text = profile.email;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'البيانات الشخصية',
        automaticallyImplyLeading: true,
      ),
      body: Consumer<UserProfileProvider>(
        builder: (context, userProfileProvider, child) {
          if (userProfileProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildProfileHeader(),
                  const SizedBox(height: 32),
                  _buildNameField(),
                  const SizedBox(height: 16),
                  _buildPhoneField(),
                  const SizedBox(height: 16),
                  _buildEmailField(),
                  const SizedBox(height: 16),
                  _buildAddressField(),
                  const SizedBox(height: 32),
                  _buildActionButtons(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            CircleAvatar(
              radius: 50,
              backgroundColor: AppTheme.primaryPurple.withOpacity(0.1),
              child: const Icon(
                Icons.person,
                size: 50,
                color: AppTheme.primaryPurple,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'البيانات الشخصية',
              style: AppTheme.headingMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'قم بإدخال بياناتك الشخصية لتظهر في التقارير والمستندات',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.mediumGray,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNameField() {
    return TextFormField(
      controller: _nameController,
      decoration: const InputDecoration(
        labelText: AppConstants.labelName,
        prefixIcon: Icon(Icons.person),
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppConstants.validationRequired;
        }
        if (!AppHelpers.isValidName(value)) {
          return AppConstants.validationMaxLength;
        }
        return null;
      },
    );
  }

  Widget _buildPhoneField() {
    return IntlPhoneField(
      controller: _phoneController,
      decoration: const InputDecoration(
        labelText: AppConstants.labelPhone,
        border: OutlineInputBorder(),
      ),
      initialCountryCode: 'SA',
      validator: (phone) {
        if (phone == null || phone.number.isEmpty) {
          return AppConstants.validationRequired;
        }
        if (!AppHelpers.isValidPhoneNumber(phone.completeNumber)) {
          return AppConstants.validationInvalidPhone;
        }
        return null;
      },
      onChanged: (phone) {
        _phoneController.text = phone.completeNumber;
      },
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      decoration: const InputDecoration(
        labelText: AppConstants.labelEmail,
        prefixIcon: Icon(Icons.email),
      ),
      keyboardType: TextInputType.emailAddress,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppConstants.validationRequired;
        }
        if (!AppHelpers.isValidEmail(value)) {
          return AppConstants.validationInvalidEmail;
        }
        return null;
      },
    );
  }

  Widget _buildAddressField() {
    return TextFormField(
      controller: _addressController,
      decoration: const InputDecoration(
        labelText: AppConstants.labelAddress,
        prefixIcon: Icon(Icons.location_on),
        alignLabelWithHint: true,
      ),
      maxLines: 3,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppConstants.validationRequired;
        }
        if (value.length > AppConstants.maxAddressLength) {
          return AppConstants.validationMaxLength;
        }
        return null;
      },
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text(AppConstants.buttonCancel),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveProfile,
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text(AppConstants.buttonSave),
          ),
        ),
      ],
    );
  }

  void _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userProfileProvider = context.read<UserProfileProvider>();
      
      await userProfileProvider.saveUserProfile(
        name: _nameController.text.trim(),
        address: _addressController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        email: _emailController.text.trim(),
      );

      AppHelpers.showSuccessSnackbar(context, 'تم حفظ البيانات بنجاح');
      Navigator.of(context).pop();
    } catch (e) {
      AppHelpers.showErrorSnackbar(context, 'حدث خطأ أثناء حفظ البيانات');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
