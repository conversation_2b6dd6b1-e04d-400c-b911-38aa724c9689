import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/category_provider.dart';
import '../providers/currency_provider.dart';
import '../providers/user_profile_provider.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';
import '../widgets/custom_app_bar.dart';
import 'category_management_screen.dart';
import 'currency_management_screen.dart';
import 'user_profile_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'الإعدادات',
        automaticallyImplyLeading: true,
      ),
      body: ListView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        children: [
          _buildProfileSection(context),
          const SizedBox(height: 24),
          _buildManagementSection(context),
          const SizedBox(height: 24),
          _buildAppSection(context),
        ],
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'البيانات الشخصية',
              style: AppTheme.headingSmall,
            ),
          ),
          Consumer<UserProfileProvider>(
            builder: (context, userProfileProvider, child) {
              return ListTile(
                leading: const CircleAvatar(
                  backgroundColor: AppTheme.primaryPurple,
                  child: Icon(Icons.person, color: AppTheme.white),
                ),
                title: Text(
                  userProfileProvider.hasProfile 
                      ? userProfileProvider.userProfile!.name
                      : 'إعداد البيانات الشخصية',
                ),
                subtitle: userProfileProvider.hasProfile 
                    ? Text(userProfileProvider.userProfile!.email)
                    : const Text('قم بإضافة بياناتك الشخصية'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _navigateToUserProfile(context),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildManagementSection(BuildContext context) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'إدارة البيانات',
              style: AppTheme.headingSmall,
            ),
          ),
          Consumer<CategoryProvider>(
            builder: (context, categoryProvider, child) {
              return ListTile(
                leading: const Icon(Icons.category, color: AppTheme.primaryPurple),
                title: const Text('إدارة الفئات'),
                subtitle: Text('${categoryProvider.categories.length} فئة'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _navigateToCategoryManagement(context),
              );
            },
          ),
          const Divider(height: 1),
          Consumer<CurrencyProvider>(
            builder: (context, currencyProvider, child) {
              return ListTile(
                leading: const Icon(Icons.monetization_on, color: AppTheme.primaryPurple),
                title: const Text('إدارة العملات'),
                subtitle: Text('${currencyProvider.currencies.length} عملة'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () => _navigateToCurrencyManagement(context),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAppSection(BuildContext context) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'التطبيق',
              style: AppTheme.headingSmall,
            ),
          ),
          ListTile(
            leading: const Icon(Icons.backup, color: AppTheme.primaryPurple),
            title: const Text('نسخ احتياطي'),
            subtitle: const Text('إنشاء نسخة احتياطية من البيانات'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showBackupDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.restore, color: AppTheme.primaryPurple),
            title: const Text('استعادة البيانات'),
            subtitle: const Text('استعادة البيانات من نسخة احتياطية'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showRestoreDialog(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.share, color: AppTheme.primaryPurple),
            title: const Text('مشاركة التطبيق'),
            subtitle: const Text('شارك التطبيق مع الآخرين'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _shareApp(context),
          ),
          const Divider(height: 1),
          ListTile(
            leading: const Icon(Icons.info, color: AppTheme.primaryPurple),
            title: const Text('حول التطبيق'),
            subtitle: Text('الإصدار ${AppConstants.appVersion}'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () => _showAboutDialog(context),
          ),
        ],
      ),
    );
  }

  void _navigateToUserProfile(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const UserProfileScreen(),
      ),
    );
  }

  void _navigateToCategoryManagement(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CategoryManagementScreen(),
      ),
    );
  }

  void _navigateToCurrencyManagement(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CurrencyManagementScreen(),
      ),
    );
  }

  void _showBackupDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نسخ احتياطي'),
        content: const Text('هل تريد إنشاء نسخة احتياطية من جميع البيانات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _createBackup(context);
            },
            child: const Text('إنشاء'),
          ),
        ],
      ),
    );
  }

  void _showRestoreDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('استعادة البيانات'),
        content: const Text('هل تريد استعادة البيانات من نسخة احتياطية؟\nسيتم استبدال جميع البيانات الحالية.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _restoreBackup(context);
            },
            child: const Text('استعادة'),
          ),
        ],
      ),
    );
  }

  void _shareApp(BuildContext context) {
    // Implement app sharing functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('مشاركة التطبيق قريباً'),
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: AppConstants.appName,
      applicationVersion: AppConstants.appVersion,
      applicationIcon: const Icon(
        Icons.account_balance_wallet,
        size: 48,
        color: AppTheme.primaryPurple,
      ),
      children: [
        const Text('تطبيق إدارة الحسابات والديون'),
        const SizedBox(height: 16),
        const Text('يساعدك في تتبع وإدارة حساباتك المالية بسهولة'),
      ],
    );
  }

  void _createBackup(BuildContext context) {
    // Implement backup functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إنشاء النسخة الاحتياطية بنجاح'),
        backgroundColor: AppTheme.success,
      ),
    );
  }

  void _restoreBackup(BuildContext context) {
    // Implement restore functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم استعادة البيانات بنجاح'),
        backgroundColor: AppTheme.success,
      ),
    );
  }
}
