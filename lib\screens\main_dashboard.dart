import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/account_provider.dart';
import '../providers/category_provider.dart';
import '../providers/currency_provider.dart';
import '../utils/app_theme.dart';
import '../utils/constants.dart';
import '../utils/helpers.dart';
import '../widgets/account_card.dart';
import '../widgets/summary_card.dart';
import '../widgets/custom_app_bar.dart';
import 'account_form_screen.dart';
import 'settings_screen.dart';

class MainDashboard extends StatefulWidget {
  const MainDashboard({super.key});

  @override
  State<MainDashboard> createState() => _MainDashboardState();
}

class _MainDashboardState extends State<MainDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() async {
    final categoryProvider = context.read<CategoryProvider>();
    final currencyProvider = context.read<CurrencyProvider>();
    final accountProvider = context.read<AccountProvider>();

    await categoryProvider.loadCategories();
    await currencyProvider.loadCurrencies();
    await accountProvider.loadAccounts();

    _tabController = TabController(
      length: categoryProvider.categories.length,
      vsync: this,
    );

    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        final selectedCategory =
            categoryProvider.categories[_tabController.index];
        accountProvider.setSelectedCategory(selectedCategory.name);
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer3<CategoryProvider, AccountProvider, CurrencyProvider>(
        builder: (
          context,
          categoryProvider,
          accountProvider,
          currencyProvider,
          child,
        ) {
          if (categoryProvider.isLoading || accountProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (categoryProvider.categories.isEmpty) {
            return _buildEmptyState();
          }

          return Column(
            children: [
              CustomAppBar(
                title: AppConstants.appName,
                isSearching: _isSearching,
                searchController: _searchController,
                onSearchChanged: (query) {
                  setState(() {
                    _searchQuery = query;
                  });
                },
                onSearchToggle: () {
                  setState(() {
                    _isSearching = !_isSearching;
                    if (!_isSearching) {
                      _searchController.clear();
                      _searchQuery = '';
                    }
                  });
                },
                actions: [
                  IconButton(
                    icon: const Icon(Icons.settings),
                    onPressed: () => _navigateToSettings(),
                  ),
                ],
              ),
              _buildTabBar(categoryProvider),
              _buildSummarySection(accountProvider, currencyProvider),
              _buildCurrencyTabs(accountProvider),
              Expanded(child: _buildAccountsList(accountProvider)),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddAccount(),
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTabBar(CategoryProvider categoryProvider) {
    return Container(
      color: AppTheme.primaryPurple,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs:
            categoryProvider.categories
                .map<Tab>((category) => Tab(text: category.name))
                .toList(),
      ),
    );
  }

  Widget _buildSummarySection(
    AccountProvider accountProvider,
    CurrencyProvider currencyProvider,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: SummaryCard(
              title: 'له',
              amount: accountProvider.totalOwedToMe,
              color: AppTheme.owedToMe,
              currencySymbol: _getCurrentCurrencySymbol(currencyProvider),
            ),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: SummaryCard(
              title: 'عليه',
              amount: accountProvider.totalIOwe,
              color: AppTheme.iOwe,
              currencySymbol: _getCurrentCurrencySymbol(currencyProvider),
            ),
          ),
          const SizedBox(width: AppConstants.smallPadding),
          Expanded(
            child: SummaryCard(
              title: 'الصافي',
              amount: accountProvider.netAmount,
              color:
                  accountProvider.netAmount >= 0
                      ? AppTheme.owedToMe
                      : AppTheme.iOwe,
              currencySymbol: _getCurrentCurrencySymbol(currencyProvider),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencyTabs(AccountProvider accountProvider) {
    final currencies = accountProvider.usedCurrencies;

    if (currencies.length <= 1) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.defaultPadding,
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: currencies.length + 1,
        itemBuilder: (context, index) {
          if (index == 0) {
            return _buildCurrencyChip(
              'الكل',
              accountProvider.selectedCurrency == null,
            );
          }

          final currency = currencies[index - 1];
          return _buildCurrencyChip(
            currency,
            accountProvider.selectedCurrency == currency,
          );
        },
      ),
    );
  }

  Widget _buildCurrencyChip(String currency, bool isSelected) {
    return Padding(
      padding: const EdgeInsets.only(right: 8.0),
      child: FilterChip(
        label: Text(currency),
        selected: isSelected,
        onSelected: (selected) {
          final accountProvider = context.read<AccountProvider>();
          if (currency == 'الكل') {
            accountProvider.setSelectedCurrency(null);
          } else {
            accountProvider.setSelectedCurrency(currency);
          }
        },
        selectedColor: AppTheme.accentPurple,
        checkmarkColor: AppTheme.primaryPurple,
      ),
    );
  }

  Widget _buildAccountsList(AccountProvider accountProvider) {
    final accounts =
        _searchQuery.isEmpty
            ? accountProvider.filteredAccounts
            : accountProvider.searchAccounts(_searchQuery);

    if (accounts.isEmpty) {
      return _buildEmptyAccountsList();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: accounts.length,
      itemBuilder: (context, index) {
        final account = accounts[index];
        return AccountCard(
          account: account,
          onTap: () => _navigateToEditAccount(account),
          onDelete: () => _deleteAccount(account.id!),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 80,
            color: AppTheme.mediumGray,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد فئات',
            style: AppTheme.headingMedium.copyWith(color: AppTheme.mediumGray),
          ),
          const SizedBox(height: 8),
          Text('قم بإضافة فئة جديدة من الإعدادات', style: AppTheme.bodyMedium),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => _navigateToSettings(),
            child: const Text('الإعدادات'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyAccountsList() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.person_add_outlined, size: 80, color: AppTheme.mediumGray),
          const SizedBox(height: 16),
          Text(
            'لا توجد حسابات',
            style: AppTheme.headingMedium.copyWith(color: AppTheme.mediumGray),
          ),
          const SizedBox(height: 8),
          Text('قم بإضافة حساب جديد', style: AppTheme.bodyMedium),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => _navigateToAddAccount(),
            child: const Text('إضافة حساب'),
          ),
        ],
      ),
    );
  }

  String _getCurrentCurrencySymbol(CurrencyProvider currencyProvider) {
    final accountProvider = context.read<AccountProvider>();
    if (accountProvider.selectedCurrency != null) {
      final currency = currencyProvider.getCurrencyByName(
        accountProvider.selectedCurrency!,
      );
      return currency?.symbol ?? '';
    }
    return currencyProvider.defaultCurrency?.symbol ?? '';
  }

  void _navigateToAddAccount() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const AccountFormScreen()));
  }

  void _navigateToEditAccount(account) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AccountFormScreen(account: account),
      ),
    );
  }

  void _navigateToSettings() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const SettingsScreen()));
  }

  void _deleteAccount(int accountId) async {
    final confirmed = await AppHelpers.showConfirmDialog(
      context,
      title: 'حذف الحساب',
      content: 'هل أنت متأكد من حذف هذا الحساب؟',
    );

    if (confirmed) {
      try {
        await context.read<AccountProvider>().deleteAccount(accountId);
        AppHelpers.showSuccessSnackbar(context, 'تم حذف الحساب بنجاح');
      } catch (e) {
        AppHelpers.showErrorSnackbar(context, 'حدث خطأ أثناء حذف الحساب');
      }
    }
  }
}
