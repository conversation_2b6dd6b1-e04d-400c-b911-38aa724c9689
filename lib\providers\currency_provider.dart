import 'package:flutter/foundation.dart';
import '../models/currency.dart';
import '../services/database_helper.dart';

class CurrencyProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<Currency> _currencies = [];
  bool _isLoading = false;

  List<Currency> get currencies => _currencies;
  bool get isLoading => _isLoading;

  Currency? get defaultCurrency {
    try {
      return _currencies.firstWhere((currency) => currency.isDefault);
    } catch (e) {
      return _currencies.isNotEmpty ? _currencies.first : null;
    }
  }

  Future<void> loadCurrencies() async {
    _isLoading = true;
    notifyListeners();

    try {
      _currencies = await _databaseHelper.getCurrencies();
    } catch (e) {
      debugPrint('Error loading currencies: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addCurrency(Currency currency) async {
    try {
      final id = await _databaseHelper.insertCurrency(currency);
      final newCurrency = currency.copyWith(id: id);
      _currencies.add(newCurrency);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding currency: $e');
      rethrow;
    }
  }

  Future<void> updateCurrency(Currency currency) async {
    try {
      await _databaseHelper.updateCurrency(currency);
      final index = _currencies.indexWhere((c) => c.id == currency.id);
      if (index != -1) {
        _currencies[index] = currency;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating currency: $e');
      rethrow;
    }
  }

  Future<void> deleteCurrency(int id) async {
    try {
      await _databaseHelper.deleteCurrency(id);
      _currencies.removeWhere((currency) => currency.id == id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting currency: $e');
      rethrow;
    }
  }

  Future<void> setDefaultCurrency(int currencyId) async {
    try {
      // First, set all currencies to non-default
      for (var currency in _currencies) {
        if (currency.isDefault) {
          final updatedCurrency = currency.copyWith(
            isDefault: false,
            updatedAt: DateTime.now(),
          );
          await _databaseHelper.updateCurrency(updatedCurrency);
        }
      }

      // Then set the selected currency as default
      final index = _currencies.indexWhere((c) => c.id == currencyId);
      if (index != -1) {
        final updatedCurrency = _currencies[index].copyWith(
          isDefault: true,
          updatedAt: DateTime.now(),
        );
        await _databaseHelper.updateCurrency(updatedCurrency);
        _currencies[index] = updatedCurrency;
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error setting default currency: $e');
      rethrow;
    }
  }

  Currency? getCurrencyByCode(String code) {
    try {
      return _currencies.firstWhere((currency) => currency.code == code);
    } catch (e) {
      return null;
    }
  }

  Currency? getCurrencyByName(String name) {
    try {
      return _currencies.firstWhere((currency) => currency.name == name);
    } catch (e) {
      return null;
    }
  }

  bool currencyExists(String code) {
    return _currencies.any((currency) => currency.code == code);
  }

  List<String> getCurrencyNames() {
    return _currencies.map((currency) => currency.name).toList();
  }

  List<String> getCurrencyCodes() {
    return _currencies.map((currency) => currency.code).toList();
  }

  String formatAmount(double amount, String currencyCode) {
    final currency = getCurrencyByCode(currencyCode);
    if (currency != null) {
      return '${currency.symbol} ${amount.toStringAsFixed(2)}';
    }
    return amount.toStringAsFixed(2);
  }

  void clearCurrencies() {
    _currencies.clear();
    notifyListeners();
  }
}
