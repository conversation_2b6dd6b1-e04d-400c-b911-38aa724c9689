import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/account.dart';
import '../models/category.dart';
import '../models/currency.dart';
import '../models/user_profile.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'accounts.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create accounts table
    await db.execute('''
      CREATE TABLE accounts(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        phoneNumber TEXT NOT NULL,
        amount REAL NOT NULL,
        details TEXT,
        currencyType TEXT NOT NULL,
        date INTEGER NOT NULL,
        debtType TEXT NOT NULL,
        category TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    ''');

    // Create categories table
    await db.execute('''
      CREATE TABLE categories(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    ''');

    // Create currencies table
    await db.execute('''
      CREATE TABLE currencies(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        symbol TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        isDefault INTEGER NOT NULL DEFAULT 0,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    ''');

    // Create user_profile table
    await db.execute('''
      CREATE TABLE user_profile(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        address TEXT,
        phoneNumber TEXT,
        email TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    ''');

    // Insert default data
    await _insertDefaultData(db);
  }

  Future<void> _insertDefaultData(Database db) async {
    final now = DateTime.now().millisecondsSinceEpoch;

    // Insert default category
    await db.insert('categories', {
      'name': 'عام',
      'description': 'الفئة العامة',
      'createdAt': now,
      'updatedAt': now,
    });

    // Insert default currencies
    await db.insert('currencies', {
      'name': 'ريال سعودي',
      'symbol': 'ر.س',
      'code': 'SAR',
      'isDefault': 1,
      'createdAt': now,
      'updatedAt': now,
    });

    await db.insert('currencies', {
      'name': 'دولار أمريكي',
      'symbol': '\$',
      'code': 'USD',
      'isDefault': 0,
      'createdAt': now,
      'updatedAt': now,
    });

    await db.insert('currencies', {
      'name': 'يورو',
      'symbol': '€',
      'code': 'EUR',
      'isDefault': 0,
      'createdAt': now,
      'updatedAt': now,
    });
  }

  // Account CRUD operations
  Future<int> insertAccount(Account account) async {
    final db = await database;
    return await db.insert('accounts', account.toMap());
  }

  Future<List<Account>> getAccounts() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('accounts');
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  Future<List<Account>> getAccountsByCategory(String category) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      where: 'category = ?',
      whereArgs: [category],
    );
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  Future<List<Account>> getAccountsByCategoryAndCurrency(
      String category, String currency) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'accounts',
      where: 'category = ? AND currencyType = ?',
      whereArgs: [category, currency],
    );
    return List.generate(maps.length, (i) => Account.fromMap(maps[i]));
  }

  Future<int> updateAccount(Account account) async {
    final db = await database;
    return await db.update(
      'accounts',
      account.toMap(),
      where: 'id = ?',
      whereArgs: [account.id],
    );
  }

  Future<int> deleteAccount(int id) async {
    final db = await database;
    return await db.delete(
      'accounts',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Category CRUD operations
  Future<int> insertCategory(Category category) async {
    final db = await database;
    return await db.insert('categories', category.toMap());
  }

  Future<List<Category>> getCategories() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('categories');
    return List.generate(maps.length, (i) => Category.fromMap(maps[i]));
  }

  Future<int> updateCategory(Category category) async {
    final db = await database;
    return await db.update(
      'categories',
      category.toMap(),
      where: 'id = ?',
      whereArgs: [category.id],
    );
  }

  Future<int> deleteCategory(int id) async {
    final db = await database;
    return await db.delete(
      'categories',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Currency CRUD operations
  Future<int> insertCurrency(Currency currency) async {
    final db = await database;
    return await db.insert('currencies', currency.toMap());
  }

  Future<List<Currency>> getCurrencies() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('currencies');
    return List.generate(maps.length, (i) => Currency.fromMap(maps[i]));
  }

  Future<int> updateCurrency(Currency currency) async {
    final db = await database;
    return await db.update(
      'currencies',
      currency.toMap(),
      where: 'id = ?',
      whereArgs: [currency.id],
    );
  }

  Future<int> deleteCurrency(int id) async {
    final db = await database;
    return await db.delete(
      'currencies',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // User Profile CRUD operations
  Future<int> insertUserProfile(UserProfile profile) async {
    final db = await database;
    return await db.insert('user_profile', profile.toMap());
  }

  Future<UserProfile?> getUserProfile() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('user_profile');
    if (maps.isNotEmpty) {
      return UserProfile.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateUserProfile(UserProfile profile) async {
    final db = await database;
    return await db.update(
      'user_profile',
      profile.toMap(),
      where: 'id = ?',
      whereArgs: [profile.id],
    );
  }

  // Statistics and aggregation methods
  Future<double> getTotalAmountByCategory(String category) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT SUM(amount) as total FROM accounts WHERE category = ?',
      [category],
    );
    return result.first['total'] as double? ?? 0.0;
  }

  Future<double> getTotalAmountByCategoryAndCurrency(
      String category, String currency) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT SUM(amount) as total FROM accounts WHERE category = ? AND currencyType = ?',
      [category, currency],
    );
    return result.first['total'] as double? ?? 0.0;
  }

  Future<Map<String, double>> getAmountsByDebtType(String category) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT debtType, SUM(amount) as total FROM accounts WHERE category = ? GROUP BY debtType',
      [category],
    );

    Map<String, double> amounts = {'له': 0.0, 'عليه': 0.0};
    for (var row in result) {
      amounts[row['debtType'] as String] = row['total'] as double? ?? 0.0;
    }
    return amounts;
  }

  Future<List<String>> getUsedCurrenciesInCategory(String category) async {
    final db = await database;
    final result = await db.rawQuery(
      'SELECT DISTINCT currencyType FROM accounts WHERE category = ?',
      [category],
    );
    return result.map((row) => row['currencyType'] as String).toList();
  }

  Future<void> close() async {
    final db = await database;
    db.close();
  }
}
